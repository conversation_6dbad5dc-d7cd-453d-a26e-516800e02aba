﻿Imports System.Windows.Forms
Imports System.Drawing
Imports System.Data
Imports System.Data.SqlClient
Imports System.IO
Imports System.Text
Imports System.Xml.Linq
Imports System.Linq
Imports Newtonsoft.Json

''' <summary>
''' 人事档案管理系统主窗体
''' 基于WindowsApp1框架的现代化人事管理系统
''' 作者：大熊
''' </summary>
Public Class Form1
    Inherits Form

    ' 系统管理器
    Private hrSystemManager As HRSystemManager
    Private currentUser As User
    Private notificationTimer As Timer
    Private notifyIcon As NotifyIcon
    Private currentSession As UserSession
    Private currentEmployee As Employee

    ' 窗体拖动相关
    Private isMouseDown As Boolean = False
    Private mouseOffset As Point

    ''' <summary>
    ''' 构造函数
    ''' </summary>
    Public Sub New()
        ' 初始化设计器组件
        InitializeComponent()

        ' 启用双缓冲以减少闪烁
        Me.SetStyle(ControlStyles.AllPaintingInWmPaint Or ControlStyles.UserPaint Or ControlStyles.DoubleBuffer, True)
        Me.UpdateStyles()

        ' 初始化系统
        InitializeHRSystemWithoutDatabase()
    End Sub

    ''' <summary>
    ''' 初始化人事系统（不包含数据库操作）
    ''' </summary>
    Private Sub InitializeHRSystemWithoutDatabase()
        Try
            ' 检查注册激活状态 - 如果失败则立即退出
            If Not CheckRegistrationStatus() Then
                Return  ' 注册检查失败，不继续执行
            End If

            ' 创建系统管理器（延迟初始化数据库）
            hrSystemManager = New HRSystemManager()

            ' 监听数据库切换事件
            AddHandler DatabaseManagerFactory.DatabaseSwitched, AddressOf OnDatabaseManagerSwitched

            ' 在启动时就初始化数据库，确保基本功能可用
            System.Diagnostics.Debug.WriteLine("启动时初始化数据库")

            Try
                ' 初始化数据库
                hrSystemManager.InitializeDatabase()
                System.Diagnostics.Debug.WriteLine("启动时数据库初始化成功")
            Catch ex As Exception
                System.Diagnostics.Debug.WriteLine($"启动时数据库初始化失败：{ex.Message}")
                ' 尝试至少升级数据库结构
                Try
                    hrSystemManager.CheckAndUpgradeDatabaseSchema()
                    System.Diagnostics.Debug.WriteLine("数据库结构升级成功")
                Catch upgradeEx As Exception
                    System.Diagnostics.Debug.WriteLine($"数据库结构升级也失败：{upgradeEx.Message}")
                End Try
            End Try

            ' 初始化通知系统
            InitializeNotificationSystem()

            ' 设置窗体属性
            Me.Text = "人事档案管理系统 v1.0 - by 大熊"
            Me.FormBorderStyle = FormBorderStyle.Sizable
            Me.StartPosition = FormStartPosition.CenterScreen
            Me.WindowState = FormWindowState.Maximized  ' 默认最大化显示
            Me.Size = New Size(1400, 800)  ' 适合1920*1080屏幕的合理大小
            Me.MinimumSize = New Size(1200, 700)  ' 最小大小
            'Me.WindowState = FormWindowState.Normal  ' 不默认最大化
            Me.ShowInTaskbar = False

            ' 初始化界面
            InitializeUI()

            ' 初始化标签页控件事件
            InitializeTabControl()

            ' 显示登录界面 - 如果失败则立即退出
            If Not ShowLoginForm() Then
                Return  ' 登录失败，不继续执行
            End If

        Catch ex As Exception
            MessageBox.Show($"系统初始化失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Environment.Exit(0)  ' 系统初始化失败，强制退出
        End Try
    End Sub



    ''' <summary>
    ''' 根据配置决定是否初始化数据库
    ''' </summary>
    Private Sub InitializeDatabaseIfNeeded()
        Try
            ' 默认进行数据库初始化，确保数据库表存在
            Dim skipDatabaseInit As Boolean = False

            ' 检查是否使用网络数据库
            Dim networkPath As String = ConfigurationService.GetValue("NetworkPath", "")

            ' 如果使用网络数据库，也需要初始化检查
            If Not String.IsNullOrEmpty(networkPath) Then
                System.Diagnostics.Debug.WriteLine("检测到网络数据库配置，仍需要初始化检查")
            End If

            ' 从配置文件读取设置
            Try
                Dim configPath = Path.Combine(Application.StartupPath, "app.config")
                If File.Exists(configPath) Then
                    Dim config = XDocument.Load(configPath)
                    Dim skipSetting = config.Descendants("add").FirstOrDefault(Function(x) x.Attribute("key")?.Value = "SkipDatabaseInitOnStartup")
                    If skipSetting IsNot Nothing Then
                        Boolean.TryParse(skipSetting.Attribute("value")?.Value, skipDatabaseInit)
                    End If
                End If
            Catch
                ' 配置读取失败，使用默认值（进行初始化）
                skipDatabaseInit = False
            End Try

            ' 也可以通过环境变量控制
            Dim envSkip = Environment.GetEnvironmentVariable("HR_SKIP_DB_INIT")
            If Not String.IsNullOrEmpty(envSkip) Then
                Boolean.TryParse(envSkip, skipDatabaseInit)
            End If

            If skipDatabaseInit Then
                System.Diagnostics.Debug.WriteLine("跳过数据库初始化")
            Else
                ' 正常初始化数据库
                System.Diagnostics.Debug.WriteLine("开始初始化数据库")
                hrSystemManager.InitializeDatabase()
                System.Diagnostics.Debug.WriteLine("数据库初始化完成")
            End If

        Catch ex As Exception
            ' 数据库初始化失败时，显示错误信息但不阻止程序启动
            System.Diagnostics.Debug.WriteLine($"数据库初始化失败：{ex.Message}")
            MessageBox.Show($"数据库初始化失败：{ex.Message}" & vbCrLf & vbCrLf &
                          "程序将继续启动，但某些功能可能不可用。" & vbCrLf &
                          "请检查数据库配置或联系管理员。",
                          "数据库初始化警告", MessageBoxButtons.OK, MessageBoxIcon.Warning)
        End Try
    End Sub

    ''' <summary>
    ''' 初始化通知系统
    ''' </summary>
    Private Sub InitializeNotificationSystem()
        Try
            ' 创建托盘图标
            notifyIcon = New NotifyIcon()
            notifyIcon.Icon = Me.Icon
            notifyIcon.Text = "人事档案管理系统"
            notifyIcon.Visible = True

            ' 创建托盘菜单
            Dim contextMenu As New ContextMenuStrip()
            contextMenu.Items.Add("显示主窗体", Nothing, AddressOf ShowMainForm)
            contextMenu.Items.Add("通知管理", Nothing, AddressOf ShowNotificationManagement)
            contextMenu.Items.Add(New ToolStripSeparator())
            contextMenu.Items.Add("退出", Nothing, AddressOf ExitApplication)
            notifyIcon.ContextMenuStrip = contextMenu

            ' 双击托盘图标显示主窗体
            AddHandler notifyIcon.DoubleClick, AddressOf ShowMainForm

            ' 创建通知检查定时器（每30分钟检查一次）
            notificationTimer = New Timer()
            notificationTimer.Interval = 30 * 60 * 1000 ' 30分钟
            AddHandler notificationTimer.Tick, AddressOf CheckNotifications
            notificationTimer.Start()

            ' 立即检查一次通知
            CheckNotifications(Nothing, Nothing)

        Catch ex As Exception
            ' 忽略通知系统初始化错误
        End Try
    End Sub



    ''' <summary>
    ''' 检查通知
    ''' </summary>
    Private Sub CheckNotifications(sender As Object, e As EventArgs)
        Try
            Dim hasNewNotifications = False
            Dim notificationCount = 0

            ' 检查生日提醒
            Dim employees = hrSystemManager.EmployeeService.GetAllEmployees("在职")
            Dim today = DateTime.Today

            For Each employee In employees
                If employee.BirthDate.HasValue Then
                    Dim birthday = New DateTime(today.Year, employee.BirthDate.Value.Month, employee.BirthDate.Value.Day)
                    If birthday = today Then
                        notificationCount += 1
                        hasNewNotifications = True
                    End If
                End If
            Next

            ' 检查合同到期提醒
            Dim expiringContracts = hrSystemManager.ContractService.GetExpiringContracts(7)
            notificationCount += expiringContracts.Count
            If expiringContracts.Count > 0 Then
                hasNewNotifications = True
            End If

            ' 显示通知
            If hasNewNotifications Then
                Dim message = $"您有 {notificationCount} 条新通知"
                notifyIcon.ShowBalloonTip(5000, "通知提醒", message, ToolTipIcon.Info)
            End If

        Catch ex As Exception
            ' 忽略通知检查错误
        End Try
    End Sub

    ''' <summary>
    ''' 显示主窗体
    ''' </summary>
    Private Sub ShowMainForm(sender As Object, e As EventArgs)
        Me.Show()
        Me.WindowState = FormWindowState.Maximized  ' 保持最大化
        Me.BringToFront()
    End Sub

    ''' <summary>
    ''' 退出应用程序
    ''' </summary>
    Private Sub ExitApplication(sender As Object, e As EventArgs)
        notifyIcon.Visible = False
        Application.Exit()
    End Sub

    ''' <summary>
    ''' 初始化用户界面
    ''' </summary>
    Private Sub InitializeUI()
        ' 创建主菜单
        CreateMainMenu()

        ' 创建工具栏
        CreateToolbar()

        ' 创建状态栏
        CreateStatusBar()

        ' 显示欢迎界面
        ShowWelcomePanel()
    End Sub

    ''' <summary>
    ''' 创建主菜单
    ''' </summary>
    Private Sub CreateMainMenu()
        ' 员工管理菜单
        Dim employeeMenu As New ToolStripMenuItem("👥 员工管理(&E)")
        employeeMenu.Font = New Font("微软雅黑", 10)

        Dim employeeListItem As New ToolStripMenuItem("📋 员工列表", Nothing, AddressOf ShowEmployeeList)
        employeeListItem.Font = New Font("微软雅黑", 9)
        employeeMenu.DropDownItems.Add(employeeListItem)

        Dim addEmployeeItem As New ToolStripMenuItem("➕ 添加员工", Nothing, AddressOf ShowAddEmployee)
        addEmployeeItem.Font = New Font("微软雅黑", 9)
        employeeMenu.DropDownItems.Add(addEmployeeItem)

        employeeMenu.DropDownItems.Add(New ToolStripSeparator())

        Dim activeEmployeesItem As New ToolStripMenuItem("✅ 在职员工", Nothing, AddressOf ShowActiveEmployees)
        activeEmployeesItem.Font = New Font("微软雅黑", 9)
        employeeMenu.DropDownItems.Add(activeEmployeesItem)

        Dim inactiveEmployeesItem As New ToolStripMenuItem("❌ 离职员工", Nothing, AddressOf ShowInactiveEmployees)
        inactiveEmployeesItem.Font = New Font("微软雅黑", 9)
        employeeMenu.DropDownItems.Add(inactiveEmployeesItem)

        employeeMenu.DropDownItems.Add(New ToolStripSeparator())

        Dim contractManagementItem As New ToolStripMenuItem("📄 合同管理", Nothing, AddressOf ShowContractManagement)
        contractManagementItem.Font = New Font("微软雅黑", 9)
        employeeMenu.DropDownItems.Add(contractManagementItem)

        ' 基础设置菜单
        Dim settingsMenu As New ToolStripMenuItem("⚙️ 基础设置(&S)")
        settingsMenu.Font = New Font("微软雅黑", 10)

        Dim deptItem As New ToolStripMenuItem("🏢 部门管理", Nothing, AddressOf ShowDepartmentManagement)
        deptItem.Font = New Font("微软雅黑", 9)
        settingsMenu.DropDownItems.Add(deptItem)

        Dim eduItem As New ToolStripMenuItem("学历设置", Nothing, AddressOf ShowEducationSettings)
        ' UIEnhancer.AddIconToMenuItem(eduItem, "book")
        settingsMenu.DropDownItems.Add(eduItem)

        Dim titleItem As New ToolStripMenuItem("职称设置", Nothing, AddressOf ShowTitleSettings)
        ' UIEnhancer.AddIconToMenuItem(titleItem, "settings")
        settingsMenu.DropDownItems.Add(titleItem)

        Dim positionItem As New ToolStripMenuItem("职务设置", Nothing, AddressOf ShowPositionSettings)
        ' UIEnhancer.AddIconToMenuItem(positionItem, "settings")
        settingsMenu.DropDownItems.Add(positionItem)

        Dim hometownItem As New ToolStripMenuItem("籍贯设置", Nothing, AddressOf ShowHometownSettings)
        ' UIEnhancer.AddIconToMenuItem(hometownItem, "home")
        settingsMenu.DropDownItems.Add(hometownItem)

        Dim excelImportItem As New ToolStripMenuItem("📊 Excel导入设置", Nothing, AddressOf ShowExcelImportSettings)
        excelImportItem.Font = New Font("微软雅黑", 9)
        settingsMenu.DropDownItems.Add(excelImportItem)

        ' 统计报表菜单
        Dim reportsMenu As New ToolStripMenuItem("统计报表(&R)")
        ' UIEnhancer.AddIconToMenuItem(reportsMenu, "chart")

        Dim empStatsItem As New ToolStripMenuItem("人员统计", Nothing, AddressOf ShowEmployeeStatistics)
        ' UIEnhancer.AddIconToMenuItem(empStatsItem, "chart")
        reportsMenu.DropDownItems.Add(empStatsItem)

        Dim deptStatsItem As New ToolStripMenuItem("部门统计", Nothing, AddressOf ShowDepartmentStatistics)
        ' UIEnhancer.AddIconToMenuItem(deptStatsItem, "chart")
        reportsMenu.DropDownItems.Add(deptStatsItem)

        Dim eduStatsItem As New ToolStripMenuItem("学历统计", Nothing, AddressOf ShowEducationStatistics)
        ' UIEnhancer.AddIconToMenuItem(eduStatsItem, "chart")
        reportsMenu.DropDownItems.Add(eduStatsItem)

        reportsMenu.DropDownItems.Add(New ToolStripSeparator())

        Dim excelItem As New ToolStripMenuItem("Excel报表", Nothing, AddressOf ShowExcelReports)
        ' UIEnhancer.AddIconToMenuItem(excelItem, "export")
        reportsMenu.DropDownItems.Add(excelItem)

        Dim printItem As New ToolStripMenuItem("打印管理", Nothing, AddressOf ShowPrintManagement)
        ' UIEnhancer.AddIconToMenuItem(printItem, "print")
        reportsMenu.DropDownItems.Add(printItem)

        Dim exportItem As New ToolStripMenuItem("导出数据", Nothing, AddressOf ExportData)
        ' UIEnhancer.AddIconToMenuItem(exportItem, "export")
        reportsMenu.DropDownItems.Add(exportItem)

        ' 系统菜单
        Dim systemMenu As New ToolStripMenuItem("系统(&Y)")
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("用户管理", Nothing, AddressOf ShowUserManagement))
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("操作日志", Nothing, AddressOf ShowOperationLog))
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("通知管理", Nothing, AddressOf ShowNotificationManagement))
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("性能监控", Nothing, AddressOf ShowPerformanceMonitor))
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("系统配置", Nothing, AddressOf ShowSystemConfig))
        systemMenu.DropDownItems.Add(New ToolStripSeparator())
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("🔧 系统设置", Nothing, AddressOf ShowSystemSettings))
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("🔄 初始化数据库", Nothing, AddressOf ShowDatabaseInitializerPanel))
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("🔄 数据库同步", Nothing, AddressOf ShowDatabaseSyncPanel))
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("🔑 注册激活", Nothing, AddressOf ShowRegistration))
        systemMenu.DropDownItems.Add(New ToolStripSeparator())
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("数据备份", Nothing, AddressOf BackupData))
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("数据恢复", Nothing, AddressOf RestoreData))

        systemMenu.DropDownItems.Add(New ToolStripMenuItem("🔧 测试同步服务", Nothing, AddressOf TestDatabaseSyncService))
        systemMenu.DropDownItems.Add(New ToolStripSeparator())
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("修改密码", Nothing, AddressOf ChangePassword))
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("注销登录", Nothing, AddressOf Logout))
        systemMenu.DropDownItems.Add(New ToolStripSeparator())
        systemMenu.DropDownItems.Add(New ToolStripMenuItem("关于系统", Nothing, AddressOf ShowAbout))



        ' 帮助菜单
        Dim helpMenu As New ToolStripMenuItem("帮助")
        helpMenu.DropDownItems.Add(New ToolStripMenuItem("帮助文档", Nothing, AddressOf ShowHelpDocument))
        helpMenu.DropDownItems.Add(New ToolStripMenuItem("使用帮助", Nothing, AddressOf ShowHelp))

        ' 添加到菜单栏
        menuStrip.Items.AddRange({employeeMenu, settingsMenu, reportsMenu, systemMenu, helpMenu})
    End Sub

    ''' <summary>
    ''' 创建工具栏
    ''' </summary>
    Private Sub CreateToolbar()
        ' 添加常用按钮
        Dim empListBtn As New ToolStripButton("员工列表", Nothing, AddressOf ShowEmployeeList)
        toolStrip.Items.Add(empListBtn)

        Dim addEmpBtn As New ToolStripButton("添加员工", Nothing, AddressOf ShowAddEmployee)
        toolStrip.Items.Add(addEmpBtn)

        toolStrip.Items.Add(New ToolStripSeparator())

        Dim deptBtn As New ToolStripButton("部门管理", Nothing, AddressOf ShowDepartmentManagement)
        toolStrip.Items.Add(deptBtn)

        toolStrip.Items.Add(New ToolStripSeparator())

        Dim statsBtn As New ToolStripButton("统计报表", Nothing, AddressOf ShowEmployeeStatistics)
        toolStrip.Items.Add(statsBtn)

        ' 搜索框
        Dim searchLabel As New ToolStripLabel("搜索：")
        Dim searchBox As New ToolStripTextBox("searchBox")
        searchBox.Size = New Size(200, 25)
        AddHandler searchBox.KeyDown, AddressOf SearchBox_KeyDown

        Dim searchButton As New ToolStripButton("搜索", Nothing, AddressOf SearchEmployees)

        Dim advancedSearchButton As New ToolStripButton("高级搜索", Nothing, AddressOf ShowAdvancedSearch)

        toolStrip.Items.Add(New ToolStripSeparator())
        toolStrip.Items.AddRange({searchLabel, searchBox, searchButton, advancedSearchButton})

        ' 应用主题
        ' ThemeManager.Instance.ApplyTheme(toolStrip)
    End Sub

    ''' <summary>
    ''' 创建状态栏
    ''' </summary>
    Private Sub CreateStatusBar()
        Dim statusLabel As New ToolStripStatusLabel("就绪")
        statusLabel.Spring = True
        statusLabel.TextAlign = ContentAlignment.MiddleLeft

        ' 获取公司名称
        Dim companyName = ConfigurationService.GetValue("CompanyName", "人事档案管理系统")
        Dim timeLabel As New ToolStripStatusLabel($"{companyName} | {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}")

        statusStrip.Items.AddRange({statusLabel, timeLabel})

        ' 定时更新时间（包含公司名称）
        Dim timer As New Timer()
        timer.Interval = 1000
        AddHandler timer.Tick, Sub()
                                   Dim currentCompanyName = ConfigurationService.GetValue("CompanyName", "人事档案管理系统")
                                   timeLabel.Text = $"{currentCompanyName} | {DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}"
                               End Sub
        timer.Start()
    End Sub

    ''' <summary>
    ''' 显示欢迎界面
    ''' </summary>
    Private Sub ShowWelcomePanel()
        ' 清空所有标签页，显示欢迎界面
        tabControl.TabPages.Clear()

        Dim welcomeTab As New TabPage("欢迎")
        welcomeTab.BackColor = Color.White

        Dim titleLabel As New Label()
        titleLabel.Text = "人事档案管理系统"
        titleLabel.Font = New Font("微软雅黑", 24, FontStyle.Bold)
        titleLabel.ForeColor = Color.FromArgb(64, 64, 64)
        titleLabel.AutoSize = True
        titleLabel.Location = New Point(50, 100)

        Dim subtitleLabel As New Label()
        subtitleLabel.Text = "现代化的人事档案管理解决方案"
        subtitleLabel.Font = New Font("微软雅黑", 12)
        subtitleLabel.ForeColor = Color.FromArgb(128, 128, 128)
        subtitleLabel.AutoSize = True
        subtitleLabel.Location = New Point(50, 150)

        Dim versionLabel As New Label()
        versionLabel.Text = "版本 1.0 - 基于WindowsApp1框架"
        versionLabel.Font = New Font("微软雅黑", 10)
        versionLabel.ForeColor = Color.FromArgb(128, 128, 128)
        versionLabel.AutoSize = True
        versionLabel.Location = New Point(50, 180)

        welcomeTab.Controls.AddRange({titleLabel, subtitleLabel, versionLabel})
        tabControl.TabPages.Add(welcomeTab)
        tabControl.SelectedTab = welcomeTab
    End Sub

    ''' <summary>
    ''' 初始化标签页控件
    ''' </summary>
    Private Sub InitializeTabControl()
        ' 绑定标签页绘制事件
        AddHandler tabControl.DrawItem, AddressOf TabControl_DrawItem
        AddHandler tabControl.MouseClick, AddressOf TabControl_MouseClick
    End Sub

    ''' <summary>
    ''' 自定义绘制标签页（添加关闭按钮）
    ''' </summary>
    Private Sub TabControl_DrawItem(sender As Object, e As DrawItemEventArgs)
        Try
            Dim tabPage = tabControl.TabPages(e.Index)
            Dim tabRect = tabControl.GetTabRect(e.Index)

            ' 绘制标签页背景
            If e.State = DrawItemState.Selected Then
                e.Graphics.FillRectangle(Brushes.White, tabRect)
            Else
                e.Graphics.FillRectangle(SystemBrushes.Control, tabRect)
            End If

            ' 绘制标签页边框
            e.Graphics.DrawRectangle(SystemPens.ControlDark, tabRect)

            ' 绘制关闭按钮（×）- 调整大小和位置
            Dim closeRect = New Rectangle(tabRect.Right - 16, tabRect.Y + 2, 12, 12)
            e.Graphics.FillRectangle(Brushes.LightGray, closeRect)
            e.Graphics.DrawRectangle(Pens.Gray, closeRect)

            ' 绘制 × 符号 - 调整线条位置
            Using pen As New Pen(Color.Black, 1)
                e.Graphics.DrawLine(pen, closeRect.X + 2, closeRect.Y + 2, closeRect.Right - 2, closeRect.Bottom - 2)
                e.Graphics.DrawLine(pen, closeRect.Right - 2, closeRect.Y + 2, closeRect.X + 2, closeRect.Bottom - 2)
            End Using

            ' 绘制标签页文本 - 调整文本区域，避免与关闭按钮重叠
            Dim textRect = New Rectangle(tabRect.X + 6, tabRect.Y + 2, tabRect.Width - 24, tabRect.Height - 4)
            TextRenderer.DrawText(e.Graphics, tabPage.Text, tabControl.Font, textRect, Color.Black, TextFormatFlags.Left Or TextFormatFlags.VerticalCenter Or TextFormatFlags.EndEllipsis)

        Catch ex As Exception
            ' 忽略绘制错误
        End Try
    End Sub

    ''' <summary>
    ''' 处理标签页鼠标点击事件
    ''' </summary>
    Private Sub TabControl_MouseClick(sender As Object, e As MouseEventArgs)
        Try
            For i As Integer = 0 To tabControl.TabPages.Count - 1
                Dim tabRect = tabControl.GetTabRect(i)
                Dim closeRect = New Rectangle(tabRect.Right - 16, tabRect.Y + 2, 12, 12)

                If closeRect.Contains(e.Location) Then
                    ' 点击了关闭按钮
                    CloseTab(i)
                    Exit For
                End If
            Next
        Catch ex As Exception
            ' 忽略点击错误
        End Try
    End Sub

    ''' <summary>
    ''' 关闭指定索引的标签页
    ''' </summary>
    Private Sub CloseTab(tabIndex As Integer)
        Try
            If tabIndex >= 0 AndAlso tabIndex < tabControl.TabPages.Count Then
                tabControl.TabPages.RemoveAt(tabIndex)

                ' 如果没有标签页了，显示欢迎界面
                If tabControl.TabPages.Count = 0 Then
                    ShowWelcomePanel()
                End If
            End If
        Catch ex As Exception
            MessageBox.Show($"关闭标签页失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 创建或切换到指定标签页
    ''' </summary>
    Private Function CreateOrSwitchToTab(title As String, control As Control) As TabPage
        ' 检查是否已存在同名标签页
        For Each tabPage As TabPage In tabControl.TabPages
            If tabPage.Text = title Then
                tabControl.SelectedTab = tabPage
                Return tabPage
            End If
        Next

        ' 创建新标签页
        Dim newTab As New TabPage(title)
        newTab.BackColor = Color.White

        ' 设置控件停靠
        control.Dock = DockStyle.Fill
        newTab.Controls.Add(control)

        ' 添加到标签页控件
        tabControl.TabPages.Add(newTab)
        tabControl.SelectedTab = newTab

        Return newTab
    End Function

    ''' <summary>
    ''' 菜单事件处理方法
    ''' </summary>

    Private Sub ShowEmployeeList(sender As Object, e As EventArgs)
        Try
            ShowEmployeeListPanel()
            UpdateStatusBar("显示员工列表")
        Catch ex As Exception
            MessageBox.Show($"显示员工列表失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowAddEmployee(sender As Object, e As EventArgs)
        Try
            ShowEmployeeEditPanel(Nothing)
            UpdateStatusBar("添加新员工")
        Catch ex As Exception
            MessageBox.Show($"显示添加员工界面失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowActiveEmployees(sender As Object, e As EventArgs)
        Try
            ShowEmployeeListPanel("在职")
            UpdateStatusBar("显示在职员工")
        Catch ex As Exception
            MessageBox.Show($"显示在职员工失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowInactiveEmployees(sender As Object, e As EventArgs)
        Try
            ShowEmployeeListPanel("离职")
            UpdateStatusBar("显示离职员工")
        Catch ex As Exception
            MessageBox.Show($"显示离职员工失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowDepartmentManagement(sender As Object, e As EventArgs)
        Try
            Dim departmentForm As New DepartmentManagementForm(hrSystemManager)
            ShowFormInMainPanel(departmentForm, "部门管理")
            UpdateStatusBar("部门管理")
        Catch ex As Exception
            MessageBox.Show($"打开部门管理失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowEducationSettings(sender As Object, e As EventArgs)
        Try
            Dim educationForm As New EducationManagementForm(hrSystemManager)
            ShowFormInMainPanel(educationForm, "学历管理")
            UpdateStatusBar("学历设置")
        Catch ex As Exception
            MessageBox.Show($"打开学历设置失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowTitleSettings(sender As Object, e As EventArgs)
        Try
            Dim titleForm As New TitleManagementForm(hrSystemManager)
            ShowFormInMainPanel(titleForm, "职称管理")
            UpdateStatusBar("职称设置")
        Catch ex As Exception
            MessageBox.Show($"打开职称设置失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowPositionSettings(sender As Object, e As EventArgs)
        Try
            Dim positionForm As New PositionManagementForm(hrSystemManager)
            ShowFormInMainPanel(positionForm, "职务管理")
            UpdateStatusBar("职务设置")
        Catch ex As Exception
            MessageBox.Show($"打开职务设置失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowHometownSettings(sender As Object, e As EventArgs)
        Try
            Dim hometownForm As New HometownManagementForm(hrSystemManager)
            ShowFormInMainPanel(hometownForm, "籍贯管理")
            UpdateStatusBar("籍贯设置")
        Catch ex As Exception
            MessageBox.Show($"打开籍贯设置失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowExcelImportSettings(sender As Object, e As EventArgs)
        Try
            Dim excelImportForm As New ExcelImportSettingsForm()
            ShowFormInMainPanel(excelImportForm, "Excel导入设置")
            UpdateStatusBar("Excel导入设置")
        Catch ex As Exception
            MessageBox.Show($"打开Excel导入设置失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowEmployeeStatistics(sender As Object, e As EventArgs)
        Try
            ShowStatisticsPanel()
            UpdateStatusBar("显示员工统计")
        Catch ex As Exception
            MessageBox.Show($"显示统计信息失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowDepartmentStatistics(sender As Object, e As EventArgs)
        Try
            ShowStatisticsPanelWithTab(1) ' 显示部门统计选项卡
            UpdateStatusBar("显示部门统计")
        Catch ex As Exception
            MessageBox.Show($"显示部门统计失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowEducationStatistics(sender As Object, e As EventArgs)
        Try
            ShowStatisticsPanelWithTab(2) ' 显示学历统计选项卡
            UpdateStatusBar("显示学历统计")
        Catch ex As Exception
            MessageBox.Show($"显示学历统计失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowExcelReports(sender As Object, e As EventArgs)
        Try
            Dim excelReportForm As New ExcelReportForm(hrSystemManager)
            ShowFormInMainPanel(excelReportForm, "Excel报表")
            UpdateStatusBar("Excel报表")
        Catch ex As Exception
            MessageBox.Show($"打开Excel报表失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowPrintManagement(sender As Object, e As EventArgs)
        Try
            Dim printForm As New PrintManagementForm(hrSystemManager)
            ShowFormInMainPanel(printForm, "打印管理")
            UpdateStatusBar("打印管理")
        Catch ex As Exception
            MessageBox.Show($"打开打印管理失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ExportData(sender As Object, e As EventArgs)
        Try
            Dim importExportForm As New DataImportExportForm(hrSystemManager)
            ShowFormInMainPanel(importExportForm, "数据导入导出")
            UpdateStatusBar("数据导入导出")
        Catch ex As Exception
            MessageBox.Show($"打开数据导入导出失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub BackupData(sender As Object, e As EventArgs)
        Try
            Dim backupForm As New BackupRestoreForm(hrSystemManager)
            ShowFormInMainPanel(backupForm, "数据备份恢复")
            UpdateStatusBar("数据备份恢复")
        Catch ex As Exception
            MessageBox.Show($"打开备份恢复失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub RestoreData(sender As Object, e As EventArgs)
        Try
            Dim backupForm As New BackupRestoreForm(hrSystemManager)
            ShowFormInMainPanel(backupForm, "数据备份恢复")
            UpdateStatusBar("数据备份恢复")
        Catch ex As Exception
            MessageBox.Show($"打开备份恢复失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowSystemConfig(sender As Object, e As EventArgs)
        Try
            Dim configForm As New SystemConfigForm(hrSystemManager)
            ShowFormInMainPanel(configForm, "系统配置")
            UpdateStatusBar("系统配置")
        Catch ex As Exception
            MessageBox.Show($"打开系统配置失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowUserManagement(sender As Object, e As EventArgs)
        Try
            If currentUser Is Nothing OrElse Not currentUser.HasPermission("用户管理") Then
                MessageBox.Show("您没有权限访问用户管理功能", "权限不足", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            Dim userForm As New UserManagementForm(hrSystemManager.UserService, currentUser)
            ShowFormInMainPanel(userForm, "用户管理")
            UpdateStatusBar("用户管理")
        Catch ex As Exception
            MessageBox.Show($"打开用户管理失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowOperationLog(sender As Object, e As EventArgs)
        Try
            MessageBox.Show("操作日志功能已简化", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            UpdateStatusBar("操作日志")
        Catch ex As Exception
            MessageBox.Show($"显示操作日志失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowPerformanceMonitor(sender As Object, e As EventArgs)
        Try
            MessageBox.Show("性能监控功能已简化", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
            UpdateStatusBar("性能监控")
        Catch ex As Exception
            MessageBox.Show($"显示性能监控失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ShowHelpDocument(sender As Object, e As EventArgs)
        Try
            Using helpForm As New HelpForm()
                helpForm.ShowDialog(Me)
            End Using
            UpdateStatusBar("帮助文档")
        Catch ex As Exception
            MessageBox.Show($"显示帮助文档失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub ChangePassword(sender As Object, e As EventArgs)
        Try
            If currentUser Is Nothing Then
                MessageBox.Show("请先登录", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If

            Dim oldPassword = InputBox("请输入当前密码：", "修改密码", "")
            If String.IsNullOrWhiteSpace(oldPassword) Then Return

            Dim newPassword = InputBox("请输入新密码（至少6位）：", "修改密码", "")
            If String.IsNullOrWhiteSpace(newPassword) Then Return

            Dim confirmPassword = InputBox("请确认新密码：", "修改密码", "")
            If newPassword <> confirmPassword Then
                MessageBox.Show("两次输入的密码不一致", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
                Return
            End If

            hrSystemManager.UserService.ChangePassword(currentUser.UserId, oldPassword, newPassword)
            MessageBox.Show("密码修改成功", "成功", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show($"修改密码失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    Private Sub Logout(sender As Object, e As EventArgs)
        If MessageBox.Show("确定要注销登录吗？", "确认注销", MessageBoxButtons.YesNo, MessageBoxIcon.Question) = DialogResult.Yes Then
            currentUser = Nothing
            currentSession = Nothing
            Me.Hide()
            ' 重新显示登录界面，如果失败则退出程序
            If Not ShowLoginForm() Then
                Environment.Exit(0)  ' 登录失败，强制退出
            End If
        End If
    End Sub

    Private Sub ShowAbout(sender As Object, e As EventArgs)
        MessageBox.Show("人事档案管理系统 v1.0" & Environment.NewLine &
                       "基于WindowsApp1框架开发" & Environment.NewLine &
                       "作者：大熊", "关于", MessageBoxButtons.OK, MessageBoxIcon.Information)
    End Sub

    Private Sub SearchBox_KeyDown(sender As Object, e As KeyEventArgs)
        If e.KeyCode = Keys.Enter Then
            SearchEmployees(sender, e)
        End If
    End Sub

    Private Sub SearchEmployees(sender As Object, e As EventArgs)
        Try
            Dim searchBox = TryCast(toolStrip.Items("searchBox"), ToolStripTextBox)
            If searchBox IsNot Nothing AndAlso Not String.IsNullOrWhiteSpace(searchBox.Text) Then
                ShowEmployeeListPanel("", searchBox.Text)
                UpdateStatusBar($"搜索员工：{searchBox.Text}")
            Else
                ShowEmployeeListPanel()
                UpdateStatusBar("显示所有员工")
            End If
        Catch ex As Exception
            MessageBox.Show($"搜索员工失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 显示高级搜索面板
    ''' </summary>
    Private Sub ShowAdvancedSearch(sender As Object, e As EventArgs)
        Try
            Dim advancedSearchForm As New AdvancedSearchForm()

            ' 绑定搜索事件
            AddHandler advancedSearchForm.SearchRequested, Sub(criteria)
                Try
                    ' 执行高级搜索
                    Dim employees = hrSystemManager.EmployeeService.AdvancedSearchEmployees(criteria)

                    ' 显示搜索结果
                    ShowEmployeeListPanel("", "", employees)

                    ' 更新状态栏
                    Dim searchInfo = BuildSearchInfo(criteria)
                    UpdateStatusBar($"高级搜索结果：{employees.Count}条记录 - {searchInfo}")
                Catch ex As Exception
                    MessageBox.Show($"搜索失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
                End Try
            End Sub

            ShowFormInMainPanel(advancedSearchForm, "高级搜索")
            UpdateStatusBar("高级搜索")
        Catch ex As Exception
            MessageBox.Show($"高级搜索失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 构建搜索信息描述
    ''' </summary>
    Private Function BuildSearchInfo(criteria As AdvancedSearchCriteria) As String
        Dim info As New List(Of String)

        If Not String.IsNullOrWhiteSpace(criteria.Name) Then
            info.Add($"姓名:{criteria.Name}")
        End If

        If Not String.IsNullOrWhiteSpace(criteria.EmployeeNumber) Then
            info.Add($"编号:{criteria.EmployeeNumber}")
        End If

        If Not String.IsNullOrWhiteSpace(criteria.Department) Then
            info.Add($"部门:{criteria.Department}")
        End If

        If Not String.IsNullOrWhiteSpace(criteria.Position) Then
            info.Add($"职位:{criteria.Position}")
        End If

        If Not String.IsNullOrWhiteSpace(criteria.Education) Then
            info.Add($"学历:{criteria.Education}")
        End If

        If Not String.IsNullOrWhiteSpace(criteria.Status) Then
            info.Add($"状态:{criteria.Status}")
        End If

        If criteria.UseHireDateRange Then
            info.Add($"入职:{criteria.HireDateFrom?.ToString("yyyy-MM-dd")}~{criteria.HireDateTo?.ToString("yyyy-MM-dd")}")
        End If

        If criteria.UseAgeRange Then
            info.Add($"年龄:{criteria.AgeFrom}~{criteria.AgeTo}岁")
        End If

        If criteria.UseSalaryRange Then
            info.Add($"薪资:{criteria.SalaryFrom}~{criteria.SalaryTo}元")
        End If

        If Not String.IsNullOrWhiteSpace(criteria.Keyword) Then
            info.Add($"关键字:{criteria.Keyword}")
        End If

        Return If(info.Count > 0, String.Join(", ", info), "无条件")
    End Function

    ''' <summary>
    ''' 更新状态栏
    ''' </summary>
    Private Sub UpdateStatusBar(message As String)
        If statusStrip.Items.Count > 0 Then
            TryCast(statusStrip.Items(0), ToolStripStatusLabel).Text = message
        End If
    End Sub

    ''' <summary>
    ''' 显示员工列表面板
    ''' </summary>
    Private Sub ShowEmployeeListPanel(Optional status As String = "", Optional keyword As String = "", Optional searchResults As List(Of Employee) = Nothing)
        Dim employeeListPanel As New EmployeeListPanel(hrSystemManager)

        ' 绑定事件
        AddHandler employeeListPanel.AddEmployeeRequested, AddressOf OnAddEmployeeRequested
        AddHandler employeeListPanel.EditEmployeeRequested, AddressOf OnEditEmployeeRequested

        ' 加载数据
        employeeListPanel.LoadEmployees(status, keyword, searchResults)

        ' 根据状态确定标签页标题
        Dim tabTitle As String = "员工列表"
        If status = "在职" Then
            tabTitle = "在职员工"
        ElseIf status = "离职" Then
            tabTitle = "离职员工"
        ElseIf Not String.IsNullOrEmpty(keyword) Then
            tabTitle = $"搜索结果 - {keyword}"
        End If

        CreateOrSwitchToTab(tabTitle, employeeListPanel)
    End Sub

    ''' <summary>
    ''' 刷新员工列表面板
    ''' </summary>
    Private Sub RefreshEmployeeListPanel()
        Try
            ' 查找现有的员工列表标签页
            Dim employeeListTab As TabPage = Nothing
            For Each tab As TabPage In tabControl.TabPages
                If tab.Text.Contains("员工列表") OrElse tab.Text.Contains("在职员工") OrElse tab.Text.Contains("离职员工") Then
                    employeeListTab = tab
                    Exit For
                End If
            Next

            If employeeListTab IsNot Nothing Then
                ' 如果找到现有的员工列表标签页，刷新其内容
                Dim employeeListPanel = TryCast(employeeListTab.Controls(0), EmployeeListPanel)
                If employeeListPanel IsNot Nothing Then
                    employeeListPanel.RefreshEmployeeList()
                    ' 切换到该标签页
                    tabControl.SelectedTab = employeeListTab
                Else
                    ' 如果面板类型不对，重新创建
                    ShowEmployeeListPanel()
                End If
            Else
                ' 如果没有找到现有的员工列表标签页，创建新的
                ShowEmployeeListPanel()
            End If

        Catch ex As Exception
            ' 如果刷新失败，回退到创建新面板
            ShowEmployeeListPanel()
        End Try
    End Sub

    ''' <summary>
    ''' 显示员工编辑面板
    ''' </summary>
    Private Sub ShowEmployeeEditPanel(employee As Employee)
        Dim editPanel As New EmployeeEditPanel(hrSystemManager, employee)

        ' 绑定事件
        AddHandler editPanel.EmployeeSaved, AddressOf OnEmployeeSaved
        AddHandler editPanel.CancelRequested, AddressOf OnEditCancelled

        Dim tabTitle As String = If(employee Is Nothing, "添加员工", $"编辑员工 - {employee.Name}")
        CreateOrSwitchToTab(tabTitle, editPanel)
    End Sub

    ''' <summary>
    ''' 显示统计面板
    ''' </summary>
    Private Sub ShowStatisticsPanel()
        ShowStatisticsPanelWithTab(0) ' 默认显示第一个选项卡
    End Sub

    ''' <summary>
    ''' 显示统计面板并选择指定选项卡
    ''' </summary>
    Private Sub ShowStatisticsPanelWithTab(tabIndex As Integer)
        Try
            Dim statistics = hrSystemManager.GetEmployeeStatistics()
            Dim statsPanel As New StatisticsPanel(statistics)

            ' 设置选中的选项卡
            If tabIndex >= 0 AndAlso tabIndex < statsPanel.TabCount Then
                statsPanel.SelectedTabIndex = tabIndex
            End If

            CreateOrSwitchToTab("统计报表", statsPanel)
        Catch ex As Exception
            MessageBox.Show($"加载统计数据失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 员工列表面板事件处理
    ''' </summary>
    Private Sub OnAddEmployeeRequested()
        ShowEmployeeEditPanel(Nothing)
    End Sub

    Private Sub OnEditEmployeeRequested(employee As Employee)
        ShowEmployeeEditPanel(employee)
    End Sub

    ''' <summary>
    ''' 员工编辑面板事件处理
    ''' </summary>
    Private Sub OnEmployeeSaved()
        ' 刷新现有的员工列表面板，如果不存在则创建新的
        RefreshEmployeeListPanel()
        UpdateStatusBar("员工信息保存成功")
    End Sub

    Private Sub OnEditCancelled()
        ShowEmployeeListPanel()
        UpdateStatusBar("取消编辑")
    End Sub

    Private Sub OnStatisticsRequested()
        ShowStatisticsPanel()
        UpdateStatusBar("显示统计报表")
    End Sub

    Private Sub OnSearchRequested()
        ShowAdvancedSearch(Nothing, Nothing)
        UpdateStatusBar("显示高级搜索")
    End Sub

    ''' <summary>
    ''' 在主面板中显示窗体控件（替代弹出式窗体）- 修复版本
    ''' </summary>
    Private Sub ShowFormInMainPanel(control As Control, title As String)
        Try
            ' 如果是Form类型，需要特殊处理
            If TypeOf control Is Form Then
                Dim form As Form = DirectCast(control, Form)

                ' 设置Form为非顶级窗体
                form.TopLevel = False
                form.FormBorderStyle = FormBorderStyle.None
                form.Dock = DockStyle.Fill

                ' 显示Form（但不作为顶级窗体）
                form.Show()
            End If

            CreateOrSwitchToTab(title, control)
        Catch ex As Exception
            MessageBox.Show($"显示窗体失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 显示登录界面
    ''' </summary>
    Private Function ShowLoginForm() As Boolean
        Try
            Using loginForm As New LoginForm(hrSystemManager.UserService)
                If loginForm.ShowDialog() = DialogResult.OK Then
                    currentUser = loginForm.LoginResult.User
                    currentSession = loginForm.LoginResult.Session
                    InitializeUserInterface()

                    ' 显示登录成功通知
                    ShowLoginSuccessNotifications()
                    Return True  ' 登录成功
                Else
                    ' 用户取消登录，立即强制退出程序
                    MessageBox.Show("用户取消登录，程序将立即退出", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information)
                    Environment.Exit(0)  ' 强制立即退出
                    Return False
                End If
            End Using
        Catch ex As Exception
            ' 登录失败时，必须立即强制退出程序以确保安全
            MessageBox.Show($"登录失败：{ex.Message}" & vbCrLf & "程序将立即退出", "登录错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Environment.Exit(0)  ' 强制立即退出
            Return False
        End Try
    End Function

    ''' <summary>
    ''' 显示登录界面（演示模式）
    ''' </summary>
    Private Sub ShowLoginFormWithDemoMode()
        Try
            ' 直接以演示模式启动，跳过登录验证
            currentUser = New User() With {
                .Username = "demo",
                .FullName = "演示用户",
                .Role = "管理员"
            }


            InitializeUserInterface()

        Catch ex As Exception
            MessageBox.Show($"演示模式启动失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Application.Exit()
        End Try
    End Sub

    ''' <summary>
    ''' 初始化用户界面
    ''' </summary>
    Private Sub InitializeUserInterface()
        Try
            ' 恢复窗体状态
            Me.WindowState = FormWindowState.Maximized  ' 保持最大化
            Me.ShowInTaskbar = True
            Me.Show()

            ' 更新窗体标题
            Me.Text = $"人事档案管理系统 - 当前用户：{currentUser.FullName} ({currentUser.Role})"

            ' 登录成功后初始化数据库
            InitializeDatabaseAfterLogin()

            ' 显示欢迎界面
            ShowWelcomePanel()

            ' 更新状态栏
            UpdateStatusBar($"欢迎 {currentUser.FullName}，您的角色是：{currentUser.Role}")

        Catch ex As Exception
            MessageBox.Show($"界面初始化失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 显示登录成功后的通知
    ''' </summary>
    Private Sub ShowLoginSuccessNotifications()
        Try
            ' 显示欢迎通知
            hrSystemManager.ToastNotificationService.ShowWelcomeNotification(currentUser.FullName)

            ' 延迟检查重要通知
            Dim timer As New Timer()
            timer.Interval = 1500 ' 1.5秒后检查
            AddHandler timer.Tick, Sub()
                                       hrSystemManager.ToastNotificationService.CheckAndShowImportantNotifications()
                                       timer.Stop()
                                       timer.Dispose()
                                   End Sub
            timer.Start()

        Catch ex As Exception
            ' 忽略通知错误，不影响主程序
            System.Diagnostics.Debug.WriteLine($"显示登录通知失败：{ex.Message}")
        End Try
    End Sub

    ''' <summary>
    ''' 登录成功后初始化数据库
    ''' </summary>
    Private Sub InitializeDatabaseAfterLogin()
        Try
            System.Diagnostics.Debug.WriteLine("开始登录后数据库初始化")

            ' 显示初始化进度
            UpdateStatusBar("正在初始化数据库...")
            Application.DoEvents()

            ' 执行数据库初始化
            hrSystemManager.InitializeDatabase()

            System.Diagnostics.Debug.WriteLine("登录后数据库初始化完成")
            UpdateStatusBar("数据库初始化完成")

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine($"登录后数据库初始化失败：{ex.Message}")

            ' 显示错误信息，但不阻止程序继续运行
            Dim errorMessage = $"数据库初始化失败：{ex.Message}" & vbCrLf & vbCrLf &
                             "可能的原因：" & vbCrLf &
                             "1. 数据库服务器未启动" & vbCrLf &
                             "2. 连接配置不正确" & vbCrLf &
                             "3. 网络连接问题" & vbCrLf & vbCrLf &
                             "程序将继续运行，但某些功能可能不可用。" & vbCrLf &
                             "您可以在设置中重新配置数据库连接。"

            MessageBox.Show(errorMessage, "数据库初始化警告", MessageBoxButtons.OK, MessageBoxIcon.Warning)
            UpdateStatusBar("数据库初始化失败 - 请检查设置")
        End Try
    End Sub

    ''' <summary>
    ''' 显示合同管理
    ''' </summary>
    Private Sub ShowContractManagement(sender As Object, e As EventArgs)
        Try
            Dim contractForm As New ContractManagementForm(hrSystemManager)
            ShowFormInMainPanel(contractForm, "合同管理")
            UpdateStatusBar("合同管理")
        Catch ex As Exception
            MessageBox.Show($"显示合同管理失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 显示通知管理
    ''' </summary>
    Private Sub ShowNotificationManagement(sender As Object, e As EventArgs)
        Try
            Dim notificationForm As New NotificationManagementForm(hrSystemManager)
            ShowFormInMainPanel(notificationForm, "消息管理")
            UpdateStatusBar("消息管理")
        Catch ex As Exception
            MessageBox.Show($"显示通知管理失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 显示帮助
    ''' </summary>
    Private Sub ShowHelp(sender As Object, e As EventArgs)
        Try
            Dim helpForm As New HelpForm()
            helpForm.ShowDialog()
        Catch ex As Exception
            MessageBox.Show($"显示帮助失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 显示系统设置
    ''' </summary>
    Private Sub ShowSystemSettings(sender As Object, e As EventArgs)
        Try
            ' 创建设置控件
            Dim settingsControl As New SettingsControl()

            ' 监听数据库切换事件
            AddHandler settingsControl.DatabaseSwitched, AddressOf OnDatabaseSwitched

            ShowFormInMainPanel(settingsControl, "系统设置")
            UpdateStatusBar("系统设置")
        Catch ex As Exception
            MessageBox.Show("显示系统设置失败：" & ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 处理数据库切换事件
    ''' </summary>
    Private Sub OnDatabaseSwitched(sender As Object, e As EventArgs)
        Try
            System.Diagnostics.Debug.WriteLine("Form1: 收到数据库切换事件")
            RefreshHRSystemManager()
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Form1: 处理数据库切换事件失败：" & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' 处理DatabaseManagerFactory的数据库切换事件
    ''' </summary>
    Private Sub OnDatabaseManagerSwitched(sender As Object, e As EventArgs)
        Try
            System.Diagnostics.Debug.WriteLine("Form1: 收到DatabaseManagerFactory数据库切换事件")
            RefreshHRSystemManager()
        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Form1: 处理DatabaseManagerFactory数据库切换事件失败：" & ex.Message)
        End Try
    End Sub



    ''' <summary>
    ''' 显示注册激活
    ''' </summary>
    Private Sub ShowRegistration(sender As Object, e As EventArgs)
        Try
            Dim registrationForm As New RegistrationForm()
            registrationForm.ShowDialog()
        Catch ex As Exception
            MessageBox.Show($"显示注册激活失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 显示数据库初始化面板
    ''' </summary>
    Private Sub ShowDatabaseInitializerPanel(sender As Object, e As EventArgs)
        Try
            Dim panel As New DatabaseInitializerPanel()
            CreateOrSwitchToTab("数据库初始化", panel)
        Catch ex As Exception
            MessageBox.Show($"显示数据库初始化面板失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

    ''' <summary>
    ''' 显示数据库同步面板
    ''' </summary>
    Private Sub ShowDatabaseSyncPanel(sender As Object, e As EventArgs)
        Try
            Dim panel As New DatabaseSyncPanel(hrSystemManager)
            CreateOrSwitchToTab("数据库同步", panel)
        Catch ex As Exception
            MessageBox.Show($"显示数据库同步面板失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub








    ''' <summary>
    ''' 检查注册激活状态
    ''' </summary>
    Private Function CheckRegistrationStatus() As Boolean
        Try
            ' 检查是否已注册
            If Not RegistrationModule.IsRegistered() Then
                ' 显示注册激活窗体
                Dim registrationForm As New RegistrationForm()
                Dim result = registrationForm.ShowDialog()

                ' 如果用户取消注册，则立即强制退出程序
                If result = DialogResult.Cancel Then
                    MessageBox.Show("软件未注册，程序将立即退出", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    Environment.Exit(0)  ' 强制立即退出
                    Return False
                End If

                ' 再次检查注册状态
                If Not RegistrationModule.IsRegistered() Then
                    MessageBox.Show("软件注册失败，程序将立即退出", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
                    Environment.Exit(0)  ' 强制立即退出
                    Return False
                End If
            End If

            Return True  ' 注册检查通过

        Catch ex As Exception
            ' 如果检查激活状态失败，必须立即强制退出程序以确保安全
            MessageBox.Show($"检查激活状态失败：{ex.Message}" & vbCrLf & "程序将立即退出", "严重错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
            Environment.Exit(0)  ' 强制立即退出
            Return False
        End Try
    End Function



    ''' <summary>
    ''' 刷新HRSystemManager（当数据库切换时调用）
    ''' </summary>
    Public Sub RefreshHRSystemManager()
        Try
            System.Diagnostics.Debug.WriteLine("Form1: 刷新HRSystemManager")

            ' 重置所有服务实例
            If hrSystemManager IsNot Nothing Then
                hrSystemManager.ResetDatabaseSyncService()
            End If

            ' 重新创建HRSystemManager实例
            hrSystemManager = New HRSystemManager()

            System.Diagnostics.Debug.WriteLine("Form1: HRSystemManager刷新完成")

        Catch ex As Exception
            System.Diagnostics.Debug.WriteLine("Form1: 刷新HRSystemManager失败：" & ex.Message)
        End Try
    End Sub

    ''' <summary>
    ''' 测试数据库同步服务
    ''' </summary>
    Private Sub TestDatabaseSyncService(sender As Object, e As EventArgs)
        Try
            Dim result = New StringBuilder()
            result.AppendLine("=== 数据库同步服务测试 ===")
            result.AppendLine()

            ' 测试当前状态
            result.AppendLine("1. 当前同步服务状态：")
            If hrSystemManager.DatabaseSyncService Is Nothing Then
                result.AppendLine("   ❌ 同步服务未初始化")

                result.AppendLine()
                result.AppendLine("2. 尝试强制初始化：")
                hrSystemManager.ForceInitializeDatabaseSyncService()

                If hrSystemManager.DatabaseSyncService Is Nothing Then
                    result.AppendLine("   ❌ 强制初始化失败")
                Else
                    result.AppendLine("   ✅ 强制初始化成功")
                End If
            Else
                result.AppendLine("   ✅ 同步服务已初始化")
            End If

            ' 测试获取状态
            result.AppendLine()
            result.AppendLine("3. 测试获取同步状态：")
            If hrSystemManager.DatabaseSyncService IsNot Nothing Then
                Try
                    Dim status = hrSystemManager.DatabaseSyncService.GetSyncStatus()
                    result.AppendLine("   ✅ 状态获取成功")
                    result.AppendLine("   状态信息：")
                    result.AppendLine(status)
                Catch ex As Exception
                    result.AppendLine("   ❌ 状态获取失败：" & ex.Message)
                End Try
            Else
                result.AppendLine("   ❌ 同步服务不可用")
            End If

            ' 显示结果
            MessageBox.Show(result.ToString(), "同步服务测试结果", MessageBoxButtons.OK, MessageBoxIcon.Information)

        Catch ex As Exception
            MessageBox.Show("测试同步服务失败：" & ex.Message, "错误", MessageBoxButtons.OK, MessageBoxIcon.Error)
        End Try
    End Sub

End Class




